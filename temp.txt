aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.4.0
akshare==1.17.16
annotated-types==0.7.0
anthropic==0.57.1
anyio==4.9.0
asttokens==3.0.0
asyncer==0.0.8
attrs==25.1.0
backoff==2.2.1
backtrader==1.9.78.123
bcrypt==4.3.0
beautifulsoup4==4.13.3
bidict==0.23.1
black==25.1.0
bs4==0.0.2
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
chainlit==2.6.0
charset-normalizer==3.4.1
chevron==0.14.0
chromadb==1.0.15
click==8.2.1
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.3.2
cssselect==1.3.0
curl_cffi==0.11.4
cycler==0.12.1
dataclasses-json==0.6.7
decorator==5.1.1
Deprecated==1.2.18
distro==1.9.0
durationpy==0.10
eodhd==1.0.32
et_xmlfile==2.0.0
executing==2.2.0
fastapi==0.115.14
feedparser==6.0.11
filelock==3.18.0
filetype==1.2.0
finnhub-python==2.4.24
flatbuffers==25.2.10
fonttools==4.58.5
frozendict==2.4.6
frozenlist==1.7.0
fsspec==2025.5.1
google-ai-generativelanguage==0.6.18
google-api-core==2.25.1
google-auth==2.40.3
googleapis-common-protos==1.70.0
greenlet==3.2.3
grpcio==1.73.1
grpcio-status==1.71.2
h11==0.16.0
html5lib==1.1
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.1
huggingface-hub==0.33.2
humanfriendly==10.0
idna==3.10
importlib_metadata==8.7.0
importlib_resources==6.5.2
inflection==0.5.1
ipython==8.32.0
jedi==0.19.2
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
jsonpatch==1.33
jsonpath==0.82.2
jsonpointer==3.0.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
kubernetes==33.1.0
langchain==0.3.26
langchain-anthropic==0.3.17
langchain-community==0.3.27
langchain-core==0.3.68
langchain-experimental==0.3.4
langchain-google-genai==2.1.6
langchain-openai==0.3.27
langchain-text-splitters==0.3.8
langgraph==0.5.1
langgraph-checkpoint==2.1.0
langgraph-prebuilt==0.5.2
langgraph-sdk==0.1.72
langsmith==0.4.4
Lazify==0.4.0
literalai==0.1.201
lxml==5.3.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
matplotlib-inline==0.1.7
mcp==1.10.1
mdurl==0.1.2
mini-racer==0.12.4
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
multidict==6.6.3
multitasking==0.0.11
mypy_extensions==1.1.0
nest-asyncio==1.6.0
numpy==2.2.3
oauthlib==3.3.1
onnxruntime==1.22.0
openai==1.93.0
openpyxl==3.1.5
opentelemetry-api==1.34.1
opentelemetry-exporter-otlp==1.34.1
opentelemetry-exporter-otlp-proto-common==1.34.1
opentelemetry-exporter-otlp-proto-grpc==1.34.1
opentelemetry-exporter-otlp-proto-http==1.34.1
opentelemetry-instrumentation==0.55b1
opentelemetry-instrumentation-alephalpha==0.40.14
opentelemetry-instrumentation-anthropic==0.40.14
opentelemetry-instrumentation-bedrock==0.40.14
opentelemetry-instrumentation-chromadb==0.40.14
opentelemetry-instrumentation-cohere==0.40.14
opentelemetry-instrumentation-crewai==0.40.14
opentelemetry-instrumentation-google-generativeai==0.40.14
opentelemetry-instrumentation-groq==0.40.14
opentelemetry-instrumentation-haystack==0.40.14
opentelemetry-instrumentation-lancedb==0.40.14
opentelemetry-instrumentation-langchain==0.40.14
opentelemetry-instrumentation-llamaindex==0.40.14
opentelemetry-instrumentation-logging==0.55b1
opentelemetry-instrumentation-marqo==0.40.14
opentelemetry-instrumentation-mcp==0.40.14
opentelemetry-instrumentation-milvus==0.40.14
opentelemetry-instrumentation-mistralai==0.40.14
opentelemetry-instrumentation-ollama==0.40.14
opentelemetry-instrumentation-openai==0.40.14
opentelemetry-instrumentation-pinecone==0.40.14
opentelemetry-instrumentation-qdrant==0.40.14
opentelemetry-instrumentation-redis==0.55b1
opentelemetry-instrumentation-replicate==0.40.14
opentelemetry-instrumentation-requests==0.55b1
opentelemetry-instrumentation-sagemaker==0.40.14
opentelemetry-instrumentation-sqlalchemy==0.55b1
opentelemetry-instrumentation-threading==0.55b1
opentelemetry-instrumentation-together==0.40.14
opentelemetry-instrumentation-transformers==0.40.14
opentelemetry-instrumentation-urllib3==0.55b1
opentelemetry-instrumentation-vertexai==0.40.14
opentelemetry-instrumentation-watsonx==0.40.14
opentelemetry-instrumentation-weaviate==0.40.14
opentelemetry-proto==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
opentelemetry-semantic-conventions-ai==0.4.9
opentelemetry-util-http==0.55b1
orjson==3.10.18
ormsgpack==1.10.0
outcome==1.3.0.post0
overrides==7.7.0
packaging==24.2
pandas==2.2.3
parsel==1.10.0
parso==0.8.4
pathspec==0.12.1
peewee==3.18.1
pillow==11.3.0
platformdirs==4.3.8
posthog==3.25.0
praw==7.8.1
prawcore==2.4.0
prompt_toolkit==3.0.50
propcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybase64==1.4.1
pycparser==2.22
pycryptodome==3.22.0
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
Pygments==2.19.1
PyJWT==2.10.1
pyparsing==3.2.3
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-engineio==4.12.2
python-multipart==0.0.18
python-socketio==5.13.0
pytz==2025.1
PyYAML==6.0.2
questionary==2.1.0
redis==6.2.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==14.0.0
rpds-py==0.26.0
rsa==4.9.1
selenium==4.29.0
setuptools==80.9.0
sgmllib3k==1.0.0
shellingham==1.5.4
simple-websocket==1.1.0
simplejson==3.20.1
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.6
SQLAlchemy==2.0.41
sse-starlette==2.4.1
stack-data==0.6.3
starlette==0.46.2
stockstats==0.6.5
sympy==1.14.0
syncer==2.0.3
tabulate==0.9.0
tenacity==9.1.2
tiktoken==0.9.0
tokenizers==0.21.2
tomli==2.2.1
tqdm==4.67.1
traceloop-sdk==0.40.14
traitlets==5.14.3
trio==0.29.0
trio-websocket==0.12.2
tushare==1.4.21
typer==0.16.0
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.12.2
tzdata==2025.1
update-checker==0.18.0
uptrace==1.34.0
urllib3==2.3.0
uv==0.7.15
uvicorn==0.35.0
w3lib==2.3.1
watchfiles==0.20.0
wcwidth==0.2.13
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
wsproto==1.2.0
xlrd==2.0.2
xlwt==1.3.0
xxhash==3.5.0
yarl==1.20.1
yfinance==0.2.65
zipp==3.23.0
zstandard==0.23.0
